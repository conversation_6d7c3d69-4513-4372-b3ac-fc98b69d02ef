"use client";

import React from 'react';
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { PrerecordedSpeakerButton } from "@/components/PrerecordedSpeakerButton";
import { AVAILABLE_TTS_VOICES, VOICE_DESCRIPTIONS, type TTSVoiceName } from "@/lib/tts-voices";
import { useSubscription } from "@/hooks/use-subscription";

interface TTSVoiceSelectorProps {
  questionVoice: string;
  answerVoice: string;
  onQuestionVoiceChange: (voice: string) => void;
  onAnswerVoiceChange: (voice: string) => void;
}

export function TTSVoiceSelector({
  questionVoice,
  answerVoice,
  onQuestionVoiceChange,
  onAnswerVoiceChange
}: TTSVoiceSelectorProps) {
  const { isPro } = useSubscription();

  // No need for sample text since we're using pre-recorded audio

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          AI Voice Selection
        </CardTitle>
        <CardDescription>
          {isPro
            ? "Choose from 15 natural AI voices to personalize your flashcard learning experience"
            : "Choose from 15 natural AI voices to personalize your flashcard learning experience. You'll need to provide your own ElevenLabs API key."
          }
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Question Voice Selection */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Label htmlFor="question-voice">Question AI Voice</Label>
            <PrerecordedSpeakerButton
              voiceName={questionVoice as TTSVoiceName}
              type="question"
              size="sm"
              variant="outline"
            />
          </div>
          <Select value={questionVoice} onValueChange={onQuestionVoiceChange}>
            <SelectTrigger id="question-voice">
              <SelectValue placeholder="Select a voice for questions" />
            </SelectTrigger>
            <SelectContent>
              {AVAILABLE_TTS_VOICES.map((voice) => (
                <SelectItem key={voice} value={voice}>
                  <div className="flex flex-col">
                    <span className="font-medium">{voice}</span>
                    <span className="text-xs text-muted-foreground">
                      {VOICE_DESCRIPTIONS[voice]}
                    </span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Answer Voice Selection */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Label htmlFor="answer-voice">Answer AI Voice</Label>
            <PrerecordedSpeakerButton
              voiceName={answerVoice as TTSVoiceName}
              type="answer"
              size="sm"
              variant="outline"
            />
          </div>
          <Select value={answerVoice} onValueChange={onAnswerVoiceChange}>
            <SelectTrigger id="answer-voice">
              <SelectValue placeholder="Select a voice for answers" />
            </SelectTrigger>
            <SelectContent>
              {AVAILABLE_TTS_VOICES.map((voice) => (
                <SelectItem key={voice} value={voice}>
                  <div className="flex flex-col">
                    <span className="font-medium">{voice}</span>
                    <span className="text-xs text-muted-foreground">
                      {VOICE_DESCRIPTIONS[voice]}
                    </span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <p className="text-sm text-muted-foreground">
          Use the speaker buttons to preview how each voice sounds with sample audio.
          Your selected voices will be used for all flashcard text-to-speech.
        </p>
      </CardContent>
    </Card>
  );
}
